"""
File Manager for BSE Corporate Announcements Scraper
Handles saving data to CSV files and managing output directories.
"""

import os
import pandas as pd
import sys
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .bse_config import bse_config as config
except ImportError:
    # Handle direct execution
    from bse_config import bse_config as config


class FileManager:
    """
    Manages file operations for BSE Corporate Announcements scraper.
    Handles CSV file creation, directory management, and file naming.
    """

    def __init__(self):
        """Initialize the file manager"""
        self.output_folder = config.OUTPUT_FOLDER
        self.ensure_output_directory()

    def ensure_output_directory(self):
        """Create output directory if it doesn't exist"""
        try:
            if not os.path.exists(self.output_folder):
                os.makedirs(self.output_folder)
                print(f"📁 Created output directory: {self.output_folder}")
            else:
                print(f"📁 Using existing output directory: {self.output_folder}")
        except Exception as e:
            print(f"❌ Error creating output directory: {e}")
            # Fallback to current directory
            self.output_folder = "."

    def save_dataframe_to_csv(self, df, start_date=None, end_date=None, suffix=""):
        """
        Save DataFrame to CSV file with timestamp.
        
        Args:
            df (pandas.DataFrame): DataFrame to save
            start_date (datetime, optional): Start date for filename
            end_date (datetime, optional): End date for filename  
            suffix (str, optional): Additional suffix for filename
            
        Returns:
            str or None: Path to saved file or None if failed
        """
        try:
            if df is None or df.empty:
                print("❌ No data to save")
                return None

            # Generate filename
            filename = self.generate_csv_filename(start_date, end_date, suffix)
            filepath = os.path.join(self.output_folder, filename)

            # Save to CSV
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            print(f"💾 Saved {len(df)} records to: {filepath}")
            print(f"📊 File size: {self.get_file_size(filepath)}")
            
            return filepath

        except Exception as e:
            print(f"❌ Error saving CSV file: {e}")
            return None

    def save_response_data(self, response_data, start_date=None, end_date=None):
        """
        Save raw API response data to file for debugging.
        
        Args:
            response_data (dict): Response data from API
            start_date (datetime, optional): Start date for filename
            end_date (datetime, optional): End date for filename
            
        Returns:
            str or None: Path to saved file or None if failed
        """
        try:
            if not response_data or 'response_text' not in response_data:
                print("❌ No response data to save")
                return None

            # Generate filename
            filename = self.generate_html_filename(start_date, end_date)
            filepath = os.path.join(self.output_folder, filename)

            # Save response text
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(response_data['response_text'])
            
            print(f"💾 Saved API response to: {filepath}")
            print(f"📊 File size: {self.get_file_size(filepath)}")
            
            return filepath

        except Exception as e:
            print(f"❌ Error saving response file: {e}")
            return None

    def generate_csv_filename(self, start_date=None, end_date=None, suffix=""):
        """
        Generate CSV filename with timestamp and date range.
        
        Args:
            start_date (datetime, optional): Start date
            end_date (datetime, optional): End date
            suffix (str, optional): Additional suffix
            
        Returns:
            str: Generated filename
        """
        timestamp = datetime.now().strftime(config.FILENAME_DATE_FORMAT)
        
        # Base filename
        filename = f"{config.CSV_FILENAME_PREFIX}_{timestamp}"
        
        # Add date range if provided
        if start_date and end_date:
            start_str = start_date.strftime("%Y%m%d")
            end_str = end_date.strftime("%Y%m%d")
            if start_str == end_str:
                filename += f"_{start_str}"
            else:
                filename += f"_{start_str}_to_{end_str}"
        
        # Add suffix if provided
        if suffix:
            filename += f"_{suffix}"
        
        return filename + config.CSV_EXTENSION

    def generate_html_filename(self, start_date=None, end_date=None):
        """
        Generate HTML/response filename with timestamp.
        
        Args:
            start_date (datetime, optional): Start date
            end_date (datetime, optional): End date
            
        Returns:
            str: Generated filename
        """
        timestamp = datetime.now().strftime(config.FILENAME_DATE_FORMAT)
        
        # Base filename
        filename = f"{config.HTML_FILENAME_PREFIX}_{timestamp}"
        
        # Add date range if provided
        if start_date and end_date:
            start_str = start_date.strftime("%Y%m%d")
            end_str = end_date.strftime("%Y%m%d")
            if start_str == end_str:
                filename += f"_{start_str}"
            else:
                filename += f"_{start_str}_to_{end_str}"
        
        return filename + config.HTML_EXTENSION

    def get_file_size(self, filepath):
        """
        Get human-readable file size.
        
        Args:
            filepath (str): Path to file
            
        Returns:
            str: Human-readable file size
        """
        try:
            size_bytes = os.path.getsize(filepath)
            
            if size_bytes < 1024:
                return f"{size_bytes} bytes"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            else:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
                
        except Exception as e:
            return f"Unknown size ({e})"

    def list_output_files(self, pattern=None):
        """
        List files in the output directory.
        
        Args:
            pattern (str, optional): Pattern to filter files
            
        Returns:
            list: List of file paths
        """
        try:
            if not os.path.exists(self.output_folder):
                return []

            files = []
            for filename in os.listdir(self.output_folder):
                filepath = os.path.join(self.output_folder, filename)
                if os.path.isfile(filepath):
                    if pattern is None or pattern in filename:
                        files.append(filepath)
            
            return sorted(files, key=os.path.getmtime, reverse=True)

        except Exception as e:
            print(f"❌ Error listing output files: {e}")
            return []

    def cleanup_old_files(self, max_files=10):
        """
        Clean up old files, keeping only the most recent ones.
        
        Args:
            max_files (int): Maximum number of files to keep
        """
        try:
            csv_files = self.list_output_files(config.CSV_FILENAME_PREFIX)
            html_files = self.list_output_files(config.HTML_FILENAME_PREFIX)
            
            # Clean up CSV files
            if len(csv_files) > max_files:
                files_to_remove = csv_files[max_files:]
                for filepath in files_to_remove:
                    try:
                        os.remove(filepath)
                        print(f"🗑️ Removed old file: {os.path.basename(filepath)}")
                    except Exception as e:
                        print(f"❌ Error removing file {filepath}: {e}")
            
            # Clean up HTML files
            if len(html_files) > max_files:
                files_to_remove = html_files[max_files:]
                for filepath in files_to_remove:
                    try:
                        os.remove(filepath)
                        print(f"🗑️ Removed old file: {os.path.basename(filepath)}")
                    except Exception as e:
                        print(f"❌ Error removing file {filepath}: {e}")

        except Exception as e:
            print(f"❌ Error during cleanup: {e}")

    def get_latest_file(self, file_type="csv"):
        """
        Get the path to the most recent file of specified type.
        
        Args:
            file_type (str): Type of file ("csv" or "html")
            
        Returns:
            str or None: Path to latest file or None if not found
        """
        try:
            if file_type.lower() == "csv":
                files = self.list_output_files(config.CSV_FILENAME_PREFIX)
            elif file_type.lower() == "html":
                files = self.list_output_files(config.HTML_FILENAME_PREFIX)
            else:
                return None
            
            return files[0] if files else None

        except Exception as e:
            print(f"❌ Error getting latest file: {e}")
            return None
