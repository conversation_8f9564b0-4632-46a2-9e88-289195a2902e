"""
Parser for BSE Corporate Announcements Data
Handles parsing and cleaning of JSON data from BSE corporate announcements API.
"""

import pandas as pd
import json
import sys
import os
import re
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .bse_config import bse_config as config
except ImportError:
    # Handle direct execution
    from bse_config import bse_config as config


class BSECorporateAnnouncementsParser:
    """
    Parses and cleans BSE corporate announcements data from API responses.
    Converts JSON data to structured pandas DataFrames.
    """

    def __init__(self):
        """Initialize the parser"""
        pass

    def parse_subject(self, subject):
        """
        Parse subject string to extract company name, BSE code, classification, and subject.

        Expected format: "Company Name - BSE Code - Classification - Subject"

        Args:
            subject (str): The subject string to parse

        Returns:
            dict: Dictionary with parsed components
        """
        if not subject or not isinstance(subject, str):
            return {
                'bse_code': None,
                'classification': None,
                'parsed_subject': None
            }

        # Split by ' - ' (space-dash-space)
        parts = subject.split(' - ')

        if len(parts) < 3:
            # If we can't parse properly, return None values
            return {
                'bse_code': None,
                'classification': None,
                'parsed_subject': subject  # Keep original as parsed_subject
            }

        # Extract components
        bse_code = parts[1].strip()

        # For classification and subject, we need to handle cases where there might be
        # multiple dashes in the classification or subject parts
        if len(parts) == 3:
            # Format: "Company - Code - Classification-Subject" or "Company - Code - Classification"
            # Need to split the third part further
            remaining = parts[2]

            # Look for common classification patterns to split properly
            classification_patterns = [
                # Pattern 1: "Announcement under Regulation XX (LODR)-Subject"
                r'^(Announcement under Regulation \d+[^-]*)-(.+)$',
                # Pattern 2: "Compliances-Subject"
                r'^(Compliances)-(.+)$',
                # Pattern 3: Generic "Classification-Subject"
                r'^([^-]+?)-(.+)$'
            ]

            classification = None
            parsed_subject = None

            for pattern in classification_patterns:
                match = re.match(pattern, remaining)
                if match:
                    classification = match.group(1).strip()
                    parsed_subject = match.group(2).strip()
                    break

            # If no pattern matched, treat entire remaining as classification
            if classification is None:
                classification = remaining.strip()
                parsed_subject = None

        elif len(parts) >= 4:
            # Format: "Company - Code - Classification - Subject - ..."
            classification = parts[2].strip()
            # Join remaining parts as subject
            parsed_subject = ' - '.join(parts[3:]).strip()

        else:
            # Less than 3 parts - can't parse properly
            classification = None
            parsed_subject = None

        return {
            'bse_code': bse_code,
            'classification': classification,
            'parsed_subject': parsed_subject
        }

    def parse_api_response(self, api_response_data):
        """
        Parse API response data and convert to DataFrame.
        
        Args:
            api_response_data (dict): Response data from DataFetcher
            
        Returns:
            pandas.DataFrame or None: Parsed data or None if failed
        """
        try:
            if not api_response_data or 'json_data' not in api_response_data:
                print("❌ No JSON data found in API response")
                return None

            json_data = api_response_data['json_data']
            
            # Extract the Table data from the JSON response
            if 'Table' not in json_data:
                print("❌ No 'Table' key found in JSON response")
                return None

            records = json_data['Table']
            
            if not records:
                print("❌ No records found in Table data")
                return None

            print(f"📊 Parsing {len(records)} corporate announcements records...")

            # Convert to DataFrame
            df = pd.DataFrame(records)
            
            if df.empty:
                print("❌ DataFrame is empty after conversion")
                return None

            print(f"✅ Successfully parsed {len(df)} records")
            print(f"📋 Columns found: {list(df.columns)}")

            # Clean and standardize the DataFrame
            cleaned_df = self.clean_dataframe(df)
            
            return cleaned_df

        except Exception as e:
            print(f"Error parsing API response: {e}")
            return None

    def parse_multiple_responses(self, response_list):
        """
        Parse multiple API responses and combine into a single DataFrame.
        
        Args:
            response_list (list): List of API response data dictionaries
            
        Returns:
            pandas.DataFrame or None: Combined parsed data or None if failed
        """
        try:
            if not response_list:
                print("❌ No response data provided")
                return None

            all_dataframes = []
            
            for i, response_data in enumerate(response_list):
                print(f"Parsing response {i+1}/{len(response_list)}...")
                df = self.parse_api_response(response_data)
                
                if df is not None and not df.empty:
                    all_dataframes.append(df)
                else:
                    print(f"⚠️ Skipping empty response {i+1}")

            if not all_dataframes:
                print("❌ No valid data found in any response")
                return None

            # Combine all DataFrames
            combined_df = pd.concat(all_dataframes, ignore_index=True)
            
            print(f"✅ Combined {len(all_dataframes)} responses into {len(combined_df)} total records")
            
            # Remove duplicates if any
            initial_count = len(combined_df)
            combined_df = combined_df.drop_duplicates()
            final_count = len(combined_df)
            
            if initial_count != final_count:
                print(f"🔄 Removed {initial_count - final_count} duplicate records")

            return combined_df

        except Exception as e:
            print(f"Error parsing multiple responses: {e}")
            return None

    def clean_dataframe(self, df):
        """
        Clean and standardize the DataFrame with custom column transformations.

        Args:
            df (pandas.DataFrame): Raw DataFrame to clean

        Returns:
            pandas.DataFrame: Cleaned and transformed DataFrame
        """
        try:
            print("🧹 Cleaning and standardizing data...")

            # Make a copy to avoid modifying the original
            cleaned_df = df.copy()

            # Remove completely empty rows and columns
            cleaned_df = cleaned_df.dropna(how='all').dropna(axis=1, how='all')

            # Apply custom column transformations
            cleaned_df = self.apply_column_transformations(cleaned_df)

            # Convert date columns if they exist
            date_columns = [col for col in cleaned_df.columns if 'date' in col.lower()]
            for col in date_columns:
                cleaned_df[col] = self.standardize_date_column(cleaned_df[col])

            # Clean text columns (remove extra whitespace)
            text_columns = cleaned_df.select_dtypes(include=['object']).columns
            for col in text_columns:
                if col not in date_columns:  # Don't clean already processed date columns
                    cleaned_df[col] = cleaned_df[col].astype(str).str.strip()
                    # Replace empty strings with NaN
                    cleaned_df[col] = cleaned_df[col].replace('', pd.NA)

            # Add metadata columns
            cleaned_df['scraped_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            print(f"✅ Data cleaning completed. Final shape: {cleaned_df.shape}")

            return cleaned_df

        except Exception as e:
            print(f"Error cleaning DataFrame: {e}")
            return df  # Return original if cleaning fails

    def apply_column_transformations(self, df):
        """
        Apply custom column transformations as per requirements.

        Args:
            df (pandas.DataFrame): DataFrame to transform

        Returns:
            pandas.DataFrame: Transformed DataFrame
        """
        try:
            print("🔄 Applying custom column transformations...")

            # Store original values needed for URL generation before removing columns
            newsid_values = df['NEWSID'].copy() if 'NEWSID' in df.columns else None
            scrip_cd_values = df['SCRIP_CD'].copy() if 'SCRIP_CD' in df.columns else None
            attachment_values = df['ATTACHMENTNAME'].copy() if 'ATTACHMENTNAME' in df.columns else None

            # Remove specified columns
            columns_to_remove = [
                'SCRIP_CD', 'XML_NAME', 'DT_TM', 'NEWS_DT', 'OLD', 'RN',
                'PDFFLAG', 'AGENDA_ID', 'TotalPageCnt'
            ]

            for col in columns_to_remove:
                if col in df.columns:
                    df = df.drop(columns=[col])
                    print(f"  ✅ Removed column: {col}")

            # Rename specified columns
            column_renames = {
                'NEWSSUB': 'subject',
                'ATTACHMENTNAME': 'attachmentfile',
                'SLONGNAME': 'company_name'
            }

            for old_name, new_name in column_renames.items():
                if old_name in df.columns:
                    df = df.rename(columns={old_name: new_name})
                    print(f"  ✅ Renamed column: {old_name} → {new_name}")

            # Add xml_data column with URL format
            if newsid_values is not None and scrip_cd_values is not None:
                df['xml_data'] = newsid_values.astype(str) + '|' + scrip_cd_values.astype(str)
                df['xml_data'] = df['xml_data'].apply(
                    lambda x: f"https://www.bseindia.com/Msource/90D/CorpXbrlGen.aspx?Bsenewid={x.split('|')[0]}&Scripcode={x.split('|')[1]}"
                    if '|' in str(x) and x.split('|')[0] != 'nan' and x.split('|')[1] != 'nan' else None
                )
                print(f"  ✅ Added xml_data column with URL format")

            # Transform attachmentfile to full URL format
            if 'attachmentfile' in df.columns and attachment_values is not None:
                df['attachmentfile'] = attachment_values.apply(
                    lambda x: f"https://www.bseindia.com/xml-data/corpfiling/AttachLive/{x}"
                    if pd.notna(x) and str(x).strip() != '' and str(x) != 'nan' else None
                )
                print(f"  ✅ Transformed attachmentfile to full URL format")

            # Parse subject column to extract BSE code, classification, and parsed subject
            if 'subject' in df.columns:
                print("🔍 Parsing subject column to extract BSE code, classification, and parsed subject...")

                # Apply subject parsing to each row
                parsed_data = df['subject'].apply(self.parse_subject)

                # Extract parsed components into separate columns
                df['bse_code'] = parsed_data.apply(lambda x: x['bse_code'])
                df['classification'] = parsed_data.apply(lambda x: x['classification'])
                df['parsed_subject'] = parsed_data.apply(lambda x: x['parsed_subject'])

                print(f"  ✅ Added bse_code column")
                print(f"  ✅ Added classification column")
                print(f"  ✅ Added parsed_subject column")

                # Show some statistics
                bse_codes_parsed = df['bse_code'].notna().sum()
                classifications_parsed = df['classification'].notna().sum()
                subjects_parsed = df['parsed_subject'].notna().sum()

                print(f"  📊 Successfully parsed BSE codes: {bse_codes_parsed}/{len(df)}")
                print(f"  📊 Successfully parsed classifications: {classifications_parsed}/{len(df)}")
                print(f"  📊 Successfully parsed subjects: {subjects_parsed}/{len(df)}")

            # Standardize remaining column names (lowercase with underscores)
            df.columns = [col.strip().lower().replace(' ', '_') for col in df.columns]

            print(f"🔄 Column transformations completed. New columns: {list(df.columns)}")

            return df

        except Exception as e:
            print(f"Error applying column transformations: {e}")
            return df  # Return original if transformation fails

    def standardize_date_column(self, date_series):
        """
        Standardize date column to a consistent format.
        
        Args:
            date_series (pandas.Series): Series containing date values
            
        Returns:
            pandas.Series: Standardized date series
        """
        try:
            # Try to convert to datetime, handling various formats
            standardized = pd.to_datetime(date_series, errors='coerce', dayfirst=True)
            
            # Count how many dates were successfully parsed
            valid_dates = standardized.notna().sum()
            total_dates = len(date_series)
            
            if valid_dates > 0:
                print(f"📅 Standardized {valid_dates}/{total_dates} dates in column")
            
            return standardized
            
        except Exception as e:
            print(f"Warning: Could not standardize date column: {e}")
            return date_series

    def get_data_summary(self, df):
        """
        Get a summary of the parsed data.
        
        Args:
            df (pandas.DataFrame): DataFrame to summarize
            
        Returns:
            dict: Summary information
        """
        try:
            if df is None or df.empty:
                return {"error": "No data to summarize"}

            summary = {
                "total_records": len(df),
                "columns": list(df.columns),
                "column_count": len(df.columns),
                "date_range": self.get_date_range(df),
                "memory_usage": f"{df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB"
            }
            
            # Add column-specific info
            summary["column_info"] = {}
            for col in df.columns:
                summary["column_info"][col] = {
                    "non_null_count": df[col].notna().sum(),
                    "data_type": str(df[col].dtype)
                }
            
            return summary

        except Exception as e:
            return {"error": f"Error generating summary: {e}"}

    def get_date_range(self, df):
        """
        Get the date range of the data.
        
        Args:
            df (pandas.DataFrame): DataFrame to analyze
            
        Returns:
            dict: Date range information
        """
        try:
            date_columns = [col for col in df.columns if 'date' in col.lower()]
            
            if not date_columns:
                return {"message": "No date columns found"}
            
            date_info = {}
            for col in date_columns:
                try:
                    date_series = pd.to_datetime(df[col], errors='coerce')
                    valid_dates = date_series.dropna()
                    
                    if not valid_dates.empty:
                        date_info[col] = {
                            "min_date": valid_dates.min().strftime('%Y-%m-%d'),
                            "max_date": valid_dates.max().strftime('%Y-%m-%d'),
                            "valid_count": len(valid_dates)
                        }
                except:
                    continue
            
            return date_info if date_info else {"message": "No valid dates found"}

        except Exception as e:
            return {"error": f"Error analyzing date range: {e}"}
