"""
BSE Corporate Announcements Scraper Package

A clean, modular BSE corporate announcements scraper with simple, readable code.
Designed for easy maintenance and understanding.

Main Features:
- Simple, readable code structure
- Modular design with focused components
- Clean error handling
- Organized file output
- Both Python API and CLI interface

Usage Examples:

Python API:
    from bse_corporate_announcements import BSECorporateAnnouncementsScraper
    
    # Basic usage
    with BSECorporateAnnouncementsScraper() as scraper:
        result = scraper.scrape_recent_data(days_back=7)
        if result:
            print(f"Scraped {result['record_count']} records")
    
    # Custom date range
    with BSECorporateAnnouncementsScraper() as scraper:
        result = scraper.scrape_date_range("01/01/2025", "07/01/2025")
        if result:
            print(f"Scraped {result['record_count']} records")

Command Line:
    python -m bse_corporate_announcements.main --days 7
    python -m bse_corporate_announcements.main --from 01/01/2025 --to 07/01/2025
"""

# Import main classes for easy access
from .scraper import BSECorporateAnnouncementsScraper
from .session import SessionManager
from .fetcher import DataFetcher
from .parser import BSECorporateAnnouncementsParser
from .file_manager import FileManager

# Import configuration for advanced users
from . import bse_config as config

# Package metadata
__version__ = "1.0.0"
__author__ = "BSE Corporate Announcements Scraper Development Team"
__description__ = "Simple, modular BSE corporate announcements scraper"

# Define what gets imported with "from bse_corporate_announcements import *"
__all__ = [
    "BSECorporateAnnouncementsScraper",
    "SessionManager",
    "DataFetcher",
    "BSECorporateAnnouncementsParser", 
    "FileManager",
    "config"
]
