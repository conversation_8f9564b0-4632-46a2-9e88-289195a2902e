"""
Database Manager for BSE Corporate Announcements Scraper
Handles database operations for storing corporate announcements data using Supabase REST API.
"""

import sys
import os
import pandas as pd
import json
import requests
from datetime import datetime

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.utils import calculate_unified_hash

try:
    from .bse_config import bse_config as config
except ImportError:
    # Handle direct execution
    from bse_config import bse_config as config


class BSECorporateAnnouncementsDatabaseManager:
    """
    Manages database operations for BSE Corporate Announcements data using Supabase REST API.
    Handles data insertion, duplicate detection, and database connectivity.
    """

    def __init__(self):
        """Initialize database manager"""
        self.supabase_url = config.SUPABASE_URL
        self.supabase_key = config.SUPABASE_ANON_KEY
        self.table_name = config.DATABASE_TABLE

        if not self.supabase_url or not self.supabase_key:
            raise Exception("Supabase configuration not found. Please check SUPABASE_URL and SUPABASE_ANON_KEY.")

        # Set up REST API endpoints
        self.rest_url = f"{self.supabase_url}/rest/v1/{self.table_name}"
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'resolution=merge-duplicates'
        }

        print(f"🗄️ Database manager initialized for table: {self.table_name}")
        print(f"🔗 Using Supabase REST API: {self.rest_url}")

    def make_request(self, method, endpoint="", data=None, params=None):
        """
        Make a REST API request to Supabase.

        Args:
            method (str): HTTP method (GET, POST, PATCH, DELETE)
            endpoint (str): Additional endpoint path
            data (dict): Request data for POST/PATCH
            params (dict): Query parameters

        Returns:
            requests.Response or None: Response object or None if failed
        """
        try:
            url = f"{self.rest_url}{endpoint}"

            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=self.headers, json=data, timeout=30)
            elif method.upper() == 'PATCH':
                response = requests.patch(url, headers=self.headers, json=data, params=params, timeout=30)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=self.headers, params=params, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response

        except requests.exceptions.RequestException as e:
            print(f"❌ Supabase API request failed: {e}")
            return None
        except Exception as e:
            print(f"❌ Error making request: {e}")
            return None

    def save_to_database(self, df):
        """
        Save DataFrame to Supabase database with duplicate detection support.

        Args:
            df (pandas.DataFrame): DataFrame to save

        Returns:
            dict: Result summary with counts and status
        """
        try:
            if df is None or df.empty:
                print("❌ No data to save to database")
                return {"success": False, "error": "No data provided"}

            print(f"💾 Saving {len(df)} records to database...")

            # Prepare data for database insertion
            prepared_df = self.prepare_dataframe_for_database(df)
            
            if prepared_df.empty:
                print("❌ No valid records after preparation")
                return {"success": False, "error": "No valid records after preparation"}

            # Check for duplicates before insertion
            duplicate_check_result = self.check_for_duplicates(prepared_df)
            
            if duplicate_check_result["has_duplicates"]:
                print(f"⚠️ Found {duplicate_check_result['duplicate_count']} potential duplicates")
                # Filter out duplicates
                new_records_df = prepared_df[~prepared_df.index.isin(duplicate_check_result["duplicate_indices"])]
            else:
                new_records_df = prepared_df

            if new_records_df.empty:
                print("ℹ️ All records are duplicates, nothing to insert")
                return {
                    "success": True,
                    "total_records": len(df),
                    "new_records": 0,
                    "duplicate_records": len(df),
                    "message": "All records were duplicates"
                }

            # Insert new records
            insert_result = self.insert_records(new_records_df)
            
            return {
                "success": insert_result["success"],
                "total_records": len(df),
                "new_records": len(new_records_df),
                "duplicate_records": len(df) - len(new_records_df),
                "inserted_records": insert_result.get("inserted_count", 0),
                "error": insert_result.get("error")
            }

        except Exception as e:
            error_msg = f"Error saving to database: {e}"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

    def prepare_dataframe_for_database(self, df):
        """
        Prepare DataFrame for database insertion with newsid → id mapping.

        Args:
            df (pandas.DataFrame): Raw DataFrame

        Returns:
            pandas.DataFrame: Prepared DataFrame
        """
        try:
            print("🔧 Preparing data for database insertion...")

            # Make a copy to avoid modifying original
            prepared_df = df.copy()

            # Map newsid to id for database primary key (UUID format)
            if 'newsid' in prepared_df.columns:
                # Validate that newsid values are valid UUIDs
                import uuid
                valid_uuids = []
                for newsid in prepared_df['newsid']:
                    try:
                        # Try to parse as UUID to validate format
                        uuid.UUID(str(newsid))
                        valid_uuids.append(True)
                    except (ValueError, TypeError):
                        valid_uuids.append(False)
                        print(f"⚠️ Invalid UUID format: {newsid}")

                # Only keep records with valid UUIDs
                prepared_df = prepared_df[valid_uuids].copy()
                prepared_df['id'] = prepared_df['newsid']
                print(f"✅ Mapped newsid → id for database primary key (UUID format)")
                print(f"📊 Validated {len(prepared_df)} records with valid UUID format")
            else:
                print("⚠️ Warning: newsid column not found")

            # Convert datetime columns to proper format
            datetime_columns = ['news_submission_dt', 'dissemdt', 'scraped_at']
            for col in datetime_columns:
                if col in prepared_df.columns:
                    try:
                        # Convert to datetime if it's a string
                        if prepared_df[col].dtype == 'object':
                            prepared_df[col] = pd.to_datetime(prepared_df[col], errors='coerce')
                        # Convert to ISO format string for database
                        prepared_df[col] = prepared_df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
                    except Exception as e:
                        print(f"⚠️ Warning: Could not convert {col} to datetime: {e}")

            # Handle numeric columns
            if 'criticalnews' in prepared_df.columns:
                prepared_df['criticalnews'] = pd.to_numeric(prepared_df['criticalnews'], errors='coerce').fillna(0).astype(int)

            if 'fld_attachsize' in prepared_df.columns:
                prepared_df['fld_attachsize'] = pd.to_numeric(prepared_df['fld_attachsize'], errors='coerce').fillna(0).astype(int)

            # Replace NaN values with None for JSON serialization
            prepared_df = prepared_df.where(pd.notna(prepared_df), None)

            # Add duplicate detection columns with default values
            prepared_df['is_duplicate'] = False
            prepared_df['pdf_hash'] = None
            prepared_df['duplicate_check_status'] = 'pending'

            # Remove any columns that don't exist in the database schema
            database_columns = [
                'id', 'subject', 'criticalnews', 'announcement_type', 'filestatus',
                'attachmentfile', 'more', 'headline', 'categoryname', 'nsurl', 'company_name',
                'news_submission_dt', 'dissemdt', 'timediff', 'fld_attachsize', 'subcatname',
                'xml_data', 'scraped_at', 'is_duplicate', 'pdf_hash', 'duplicate_check_status',
                'bse_code', 'classification', 'parsed_subject'
            ]

            # Keep only columns that exist in both the dataframe and database schema
            columns_to_keep = [col for col in database_columns if col in prepared_df.columns]
            prepared_df = prepared_df[columns_to_keep]

            print(f"✅ Prepared {len(prepared_df)} records for database")
            print(f"📋 Columns prepared: {list(prepared_df.columns)}")
            return prepared_df

        except Exception as e:
            print(f"❌ Error preparing data for database: {e}")
            return pd.DataFrame()

    def check_for_duplicates(self, df):
        """
        Check for duplicate records in the database using id column.

        Args:
            df (pandas.DataFrame): DataFrame to check

        Returns:
            dict: Duplicate check results
        """
        try:
            print("🔍 Checking for duplicate records...")

            if 'id' not in df.columns:
                print("❌ No id column found")
                return {"has_duplicates": False, "duplicate_count": 0, "duplicate_indices": []}

            # Get unique IDs from the DataFrame
            ids_to_check = df['id'].dropna().unique().tolist()

            if not ids_to_check:
                print("⚠️ No valid IDs to check")
                return {"has_duplicates": False, "duplicate_count": 0, "duplicate_indices": []}

            print(f"🔍 Checking {len(ids_to_check)} unique IDs against database...")

            # Query Supabase to check for existing records
            existing_ids = self.query_existing_ids(ids_to_check)

            # Find duplicate indices
            duplicate_indices = df[df['id'].isin(existing_ids)].index.tolist()

            result = {
                "has_duplicates": len(duplicate_indices) > 0,
                "duplicate_count": len(duplicate_indices),
                "duplicate_indices": duplicate_indices,
                "existing_ids": existing_ids
            }

            if result["has_duplicates"]:
                print(f"⚠️ Found {result['duplicate_count']} duplicate records")
            else:
                print("✅ No duplicates found")

            return result

        except Exception as e:
            print(f"❌ Error checking for duplicates: {e}")
            return {"has_duplicates": False, "duplicate_count": 0, "duplicate_indices": []}

    def query_existing_ids(self, ids_list):
        """
        Query Supabase to check which IDs already exist using REST API.

        Args:
            ids_list (list): List of IDs to check

        Returns:
            list: List of existing IDs
        """
        try:
            if not ids_list:
                return []

            print(f"🔍 Querying database for existing records...")

            # Use Supabase REST API to check for existing records
            # Build the filter for multiple IDs
            ids_filter = ','.join(f'"{id_val}"' for id_val in ids_list)
            params = {
                'select': 'id',
                'id': f'in.({ids_filter})'
            }

            response = self.make_request('GET', params=params)

            if response and response.status_code == 200:
                result = response.json()
                existing_ids = [row.get('id') for row in result if row.get('id')]
                print(f"📊 Found {len(existing_ids)} existing records")
                return existing_ids
            else:
                print("📊 No existing records found")
                return []

        except Exception as e:
            print(f"❌ Error querying existing IDs: {e}")
            return []

    def insert_records(self, df):
        """
        Insert records into the database using Supabase MCP.

        Args:
            df (pandas.DataFrame): DataFrame with records to insert

        Returns:
            dict: Insertion result
        """
        try:
            print(f"📤 Inserting {len(df)} records into database...")

            # Convert DataFrame to list of dictionaries
            records = df.to_dict('records')

            if not records:
                return {
                    "success": False,
                    "inserted_count": 0,
                    "error": "No records to insert"
                }

            # Insert records using Supabase MCP
            success_count = 0
            failed_records = []

            # Insert records in batches to avoid overwhelming the database
            batch_size = 10
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]

                try:
                    # Create INSERT query for the batch
                    insert_result = self.insert_batch(batch)
                    if insert_result:
                        success_count += len(batch)
                        print(f"✅ Inserted batch {i//batch_size + 1}: {len(batch)} records")
                    else:
                        failed_records.extend(batch)
                        print(f"❌ Failed to insert batch {i//batch_size + 1}")

                except Exception as batch_error:
                    print(f"❌ Error inserting batch {i//batch_size + 1}: {batch_error}")
                    failed_records.extend(batch)

            if success_count > 0:
                print(f"✅ Successfully inserted {success_count} records")

            if failed_records:
                print(f"⚠️ Failed to insert {len(failed_records)} records")

            return {
                "success": success_count > 0,
                "inserted_count": success_count,
                "failed_count": len(failed_records),
                "message": f"Inserted {success_count} records successfully"
            }

        except Exception as e:
            error_msg = f"Error inserting records: {e}"
            print(f"❌ {error_msg}")
            return {
                "success": False,
                "inserted_count": 0,
                "error": error_msg
            }

    def insert_batch(self, records_batch):
        """
        Insert a batch of records using Supabase REST API.

        Args:
            records_batch (list): List of record dictionaries to insert

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not records_batch:
                return False

            print(f"💾 Inserting batch of {len(records_batch)} records...")

            # Use upsert to handle conflicts (insert or update if exists)
            headers = self.headers.copy()
            headers['Prefer'] = 'resolution=merge-duplicates'

            # Make the POST request to insert/upsert records
            response = requests.post(
                self.rest_url,
                headers=headers,
                json=records_batch,
                timeout=60
            )

            if response.status_code in [200, 201]:
                print(f"✅ Successfully inserted/updated {len(records_batch)} records")
                return True
            else:
                print(f"❌ Failed to insert batch. Status: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                return False

        except Exception as e:
            print(f"❌ Error in insert_batch: {e}")
            return False

    def test_connection(self):
        """
        Test database connection using Supabase REST API.

        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            print("🔌 Testing database connection...")

            # Test connection by making a simple query to count records
            params = {
                'select': 'count',
                'limit': '1'
            }

            response = self.make_request('GET', params=params)

            if response and response.status_code == 200:
                result = response.json()
                record_count = len(result) if isinstance(result, list) else 0
                print(f"✅ Database connection successful")
                print(f"🔗 Connected to table: {self.table_name}")
                print(f"📊 Table accessible (response received)")
                return True
            else:
                print("❌ Database connection failed")
                return False

        except Exception as e:
            print(f"❌ Database connection test failed: {e}")
            return False

    def get_table_info(self):
        """
        Get information about the database table using Supabase REST API.

        Returns:
            dict: Table information
        """
        try:
            # Get basic table statistics
            params = {
                'select': 'id,created_at',
                'order': 'created_at.desc',
                'limit': '1'
            }

            response = self.make_request('GET', params=params)

            if response and response.status_code == 200:
                result = response.json()

                # Get total count
                count_params = {'select': 'count'}
                count_response = self.make_request('GET', params=count_params)
                record_count = len(count_response.json()) if count_response and count_response.status_code == 200 else 0

                return {
                    "table_name": self.table_name,
                    "rest_url": self.rest_url,
                    "record_count": record_count,
                    "latest_record": result[0].get('created_at') if result else None,
                    "status": "connected"
                }
            else:
                return {
                    "table_name": self.table_name,
                    "rest_url": self.rest_url,
                    "status": "connection_failed"
                }

        except Exception as e:
            return {
                "table_name": self.table_name,
                "rest_url": self.rest_url,
                "error": str(e),
                "status": "error"
            }

    def trigger_duplicate_detection(self, record_id, pdf_url):
        """
        Trigger PDF duplicate detection for a specific record via Edge Function

        Args:
            record_id (str): The record ID
            pdf_url (str): The PDF URL to check

        Returns:
            bool: True if trigger was successful
        """
        try:
            if not pdf_url or pdf_url.strip() == '':
                return True  # No PDF to check

            # Prepare the request to Edge Function
            edge_function_url = f"{self.supabase_url}/functions/v1/pdf-duplicate-detector"

            payload = {
                "record_id": str(record_id),
                "table_name": "bse_corporate_announcements",
                "pdf_url": pdf_url
            }

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.supabase_key}'
            }

            # Make async call to Edge Function (fire and forget)
            response = requests.post(
                edge_function_url,
                headers=headers,
                json=payload,
                timeout=5  # Short timeout since this is fire-and-forget
            )

            if response.ok:
                print(f"✅ Triggered duplicate detection for record {record_id}")
                return True
            else:
                print(f"⚠️ Failed to trigger duplicate detection for record {record_id}: {response.status_code}")
                return False

        except Exception as e:
            print(f"⚠️ Error triggering duplicate detection for record {record_id}: {e}")
            return False

    def _trigger_duplicate_detection_batch(self, records):
        """
        Trigger duplicate detection for a batch of records

        Args:
            records (list): List of record dictionaries
        """
        try:
            triggered_count = 0
            for record in records:
                record_id = record.get('id')
                pdf_url = record.get('attachmentfile')

                if record_id and pdf_url:
                    if self.trigger_duplicate_detection(record_id, pdf_url):
                        triggered_count += 1

            if triggered_count > 0:
                print(f"✅ Triggered duplicate detection for {triggered_count} records")

        except Exception as e:
            print(f"⚠️ Error triggering batch duplicate detection: {e}")

    def get_duplicate_statistics(self):
        """
        Get duplicate detection statistics via Edge Function

        Returns:
            dict: Statistics data or empty dict if failed
        """
        try:
            edge_function_url = f"{self.supabase_url}/functions/v1/get-duplicate-stats"

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.supabase_key}'
            }

            response = requests.post(
                edge_function_url,
                headers=headers,
                json={},
                timeout=30
            )

            if response.ok:
                return response.json()
            else:
                print(f"⚠️ Failed to get duplicate statistics: {response.status_code}")
                return {}

        except Exception as e:
            print(f"⚠️ Error getting duplicate statistics: {e}")
            return {}
