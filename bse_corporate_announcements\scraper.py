"""
Main BSE Corporate Announcements Scraper Class
Orchestrates all components to provide a clean, simple interface.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .session import SessionManager
    from .fetcher import DataFetcher
    from .parser import BSECorporateAnnouncementsParser
    from .file_manager import FileManager
    from .database_manager import BSECorporateAnnouncementsDatabaseManager
    from .bse_config import bse_config as config, DEFAULT_DAYS_BACK, SAVE_CSV, USE_DATABASE
except ImportError:
    # Handle direct execution
    from session import SessionManager
    from fetcher import DataFetcher
    from parser import BSECorporateAnnouncementsParser
    from file_manager import FileManager
    from database_manager import BSECorporateAnnouncementsDatabaseManager
    from bse_config import bse_config as config, DEFAULT_DAYS_BACK, SAVE_CSV, USE_DATABASE


class BSECorporateAnnouncementsScraper:
    """
    Main BSE Corporate Announcements scraper class that coordinates all operations.
    Provides simple methods for scraping corporate announcements data.
    """

    def __init__(self):
        """Initialize all components of the scraper"""
        print("🏢 BSE Corporate Announcements Scraper")
        print("=" * 50)
        print("Initializing BSE Corporate Announcements scraper...")

        # Initialize all components
        self.session_manager = SessionManager()
        self.data_fetcher = DataFetcher(self.session_manager)
        self.parser = BSECorporateAnnouncementsParser()
        self.file_manager = FileManager()

        # Initialize database manager if enabled
        self.database_manager = None
        if USE_DATABASE:
            try:
                self.database_manager = BSECorporateAnnouncementsDatabaseManager()
                print("✅ Database integration enabled")
            except Exception as e:
                print(f"⚠️ Database integration failed: {e}")
                print("⚠️ Continuing with CSV-only mode")

        print("✅ BSE Corporate Announcements scraper initialized successfully")

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if exc_type:
            print(f"❌ Scraper exited with error: {exc_val}")
        else:
            print("✅ Scraper completed successfully")

    def scrape_recent_data(self, days_back=DEFAULT_DAYS_BACK):
        """
        Scrape corporate announcements data for the last N days.
        
        Args:
            days_back (int): Number of days back to scrape
            
        Returns:
            dict or None: Results dictionary or None if failed
        """
        try:
            print(f"📅 Scraping BSE Corporate Announcements for the last {days_back} days...")

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)

            print(f"📅 Date range: {start_date.strftime('%d/%m/%Y')} to {end_date.strftime('%d/%m/%Y')}")

            # Test connection first
            if not self.test_connection():
                return None

            return self.scrape_date_range_internal(start_date, end_date)

        except Exception as e:
            print(f"❌ Error in scrape_recent_data: {e}")
            return None

    def scrape_date_range(self, start_date_str, end_date_str):
        """
        Scrape data for a specific date range.
        
        Args:
            start_date_str (str): Start date in DD/MM/YYYY format
            end_date_str (str): End date in DD/MM/YYYY format
            
        Returns:
            dict or None: Results dictionary or None if failed
        """
        try:
            print(f"📅 Scraping BSE Corporate Announcements from {start_date_str} to {end_date_str}")

            # Parse date strings
            start_date = datetime.strptime(start_date_str, '%d/%m/%Y')
            end_date = datetime.strptime(end_date_str, '%d/%m/%Y')

            # Test connection first
            if not self.test_connection():
                return None

            return self.scrape_date_range_internal(start_date, end_date)

        except Exception as e:
            print(f"❌ Error in scrape_date_range: {e}")
            return None

    def scrape_date_range_internal(self, start_date, end_date):
        """
        Internal method to scrape data for a date range.
        
        Args:
            start_date (datetime): Start date
            end_date (datetime): End date
            
        Returns:
            dict or None: Results dictionary or None if failed
        """
        try:
            # Fetch data from API (all pages)
            print("🔄 Fetching data from BSE Corporate Announcements API...")
            response_data_list = self.data_fetcher.fetch_all_pages(start_date, end_date)

            if not response_data_list:
                print("❌ Failed to fetch data from BSE API")
                return None

            print(f"✅ Fetched {len(response_data_list)} pages of data")

            # Parse the data
            print("📊 Parsing corporate announcements data...")
            df = self.parser.parse_multiple_responses(response_data_list)

            if df is None or df.empty:
                print("❌ No data found after parsing")
                return None

            print(f"✅ Parsed {len(df)} corporate announcements records")

            # Get data summary
            summary = self.parser.get_data_summary(df)
            print(f"📋 Data summary: {summary.get('total_records', 0)} records, {summary.get('column_count', 0)} columns")

            # Save results
            result = {
                'dataframe': df,
                'record_count': len(df),
                'start_date': start_date,
                'end_date': end_date,
                'summary': summary,
                'files_saved': []
            }

            # Save to CSV if enabled
            if SAVE_CSV:
                csv_path = self.file_manager.save_dataframe_to_csv(df, start_date, end_date)
                if csv_path:
                    result['files_saved'].append(csv_path)

            # Save to database if enabled
            if USE_DATABASE and self.database_manager:
                print("💾 Saving to database...")
                db_result = self.database_manager.save_to_database(df)
                result['database_result'] = db_result
                
                if db_result.get('success'):
                    print(f"✅ Database save successful: {db_result.get('new_records', 0)} new records")
                else:
                    print(f"❌ Database save failed: {db_result.get('error', 'Unknown error')}")

            # Save raw response for debugging
            if response_data_list:
                response_path = self.file_manager.save_response_data(response_data_list[0], start_date, end_date)
                if response_path:
                    result['files_saved'].append(response_path)

            print(f"🎉 Successfully scraped {result['record_count']} corporate announcements!")
            return result

        except Exception as e:
            print(f"❌ Error in scrape_date_range_internal: {e}")
            return None

    def test_connection(self):
        """
        Test connection to BSE Corporate Announcements API.
        
        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            print("🔌 Testing BSE Corporate Announcements API connection...")
            
            # Test session manager connection
            if not self.session_manager.test_connection():
                print("❌ Session manager connection test failed")
                return False

            # Test data fetcher API connection
            if not self.data_fetcher.test_api_connection():
                print("❌ Data fetcher API connection test failed")
                return False

            # Test database connection if enabled
            if USE_DATABASE and self.database_manager:
                if not self.database_manager.test_connection():
                    print("⚠️ Database connection test failed, but continuing with CSV-only mode")

            print("✅ All connection tests passed")
            return True

        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False

    def get_scraper_info(self):
        """
        Get information about the scraper configuration.
        
        Returns:
            dict: Scraper information
        """
        info = {
            "scraper_name": "BSE Corporate Announcements Scraper",
            "version": "1.0.0",
            "api_base_url": config.API_BASE_URL,
            "api_endpoint": config.CORPORATE_ANNOUNCEMENTS_API_URL,
            "output_folder": config.OUTPUT_FOLDER,
            "csv_enabled": SAVE_CSV,
            "database_enabled": USE_DATABASE,
            "default_days_back": DEFAULT_DAYS_BACK
        }

        if self.database_manager:
            info["database_info"] = self.database_manager.get_table_info()

        return info

    def cleanup_old_files(self, max_files=10):
        """
        Clean up old output files.
        
        Args:
            max_files (int): Maximum number of files to keep
        """
        try:
            print(f"🧹 Cleaning up old files (keeping {max_files} most recent)...")
            self.file_manager.cleanup_old_files(max_files)
            print("✅ File cleanup completed")
        except Exception as e:
            print(f"❌ Error during file cleanup: {e}")

    def get_latest_data_file(self):
        """
        Get path to the most recent CSV data file.
        
        Returns:
            str or None: Path to latest CSV file or None if not found
        """
        return self.file_manager.get_latest_file("csv")
