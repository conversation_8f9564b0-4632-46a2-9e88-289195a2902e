"""
Database Manager for BSE Insider Trading Scraper
Handles Supabase database operations with duplicate detection and batch processing.
"""

import requests
import json
import csv
import time
import re
import os
from datetime import datetime
from .config import SUPABASE_URL, SUPABASE_ANON_KEY
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from shared.utils import calculate_unified_hash, fetch_cross_reference_code


class BSEDatabaseManager:
    """Manages database operations for BSE insider trading data"""
    
    def __init__(self):
        """Initialize database manager with Supabase configuration"""
        self.supabase_url = SUPABASE_URL
        self.supabase_key = SUPABASE_ANON_KEY
        self.table_name = "bse_insider_trading"

        # Setup headers for Supabase REST API
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }

        # Cache for cross-reference lookups to avoid repeated API calls
        self.cross_reference_cache = {}
        
        print("✅ BSE Database manager initialized")
    
    def save_records(self, records):
        """
        Save BSE records to Supabase database with duplicate detection.
        
        Args:
            records: List of dictionaries containing BSE insider trading data
            
        Returns:
            dict: Summary of insertion results
        """
        if not records:
            print("No BSE records to save to database")
            return {'success': True, 'inserted': 0, 'duplicates': 0, 'errors': 0}
        
        try:
            print(f"🔄 Processing {len(records)} BSE records for database insertion...")
            
            # Convert DataFrame records to database format
            db_records = self._convert_records_to_db_format(records)
            
            # Check for duplicates using batch hash checking
            new_records = self._filter_duplicate_records(db_records)
            duplicate_count = len(db_records) - len(new_records)
            
            if duplicate_count > 0:
                print(f"🔍 Found {duplicate_count} duplicate BSE records in database")
            
            if not new_records:
                print("📊 No new BSE records to insert (all were duplicates)")
                return {
                    'success': True,
                    'inserted': 0,
                    'duplicates': duplicate_count,
                    'errors': 0
                }
            
            # Insert new records
            total_inserted, total_errors, duplicates_at_insert = self._insert_records(new_records)
            
            print(f"\n📊 BSE DATABASE INSERTION SUMMARY:")
            print(f"   Total records processed: {len(records)}")
            print(f"   Duplicates skipped (pre-check): {duplicate_count}")
            print(f"   Duplicates found at insert: {duplicates_at_insert}")
            print(f"   New records inserted: {total_inserted}")
            print(f"   Errors: {total_errors}")
            
            return {
                'success': total_errors == 0,
                'inserted': total_inserted,
                'duplicates': duplicate_count + duplicates_at_insert,
                'errors': total_errors
            }
            
        except Exception as e:
            print(f"❌ Error saving BSE records to database: {e}")
            return {'success': False, 'inserted': 0, 'duplicates': 0, 'errors': len(records)}
    
    def _convert_records_to_db_format(self, records):
        """Convert DataFrame records to database-compatible format"""
        db_records = []
        total_records = len(records)

        print(f"🔄 Converting {total_records} BSE records to database format...")

        for i, record in enumerate(records, 1):
            # Show progress for large datasets
            if total_records > 100 and i % 500 == 0:
                print(f"📊 Processing record {i}/{total_records} ({(i/total_records)*100:.1f}%)")

            # Map CSV column names to database column names (using actual CSV column names from download)
            db_record = {
                'security_code': self._clean_value(record.get('Security Code', '')),
                'security_name': self._clean_value(record.get('Security Name', '')),
                'name_of_person': self._clean_value(record.get('Name of Person', '')),
                'category_of_person': self._clean_value(record.get('Category of person', '')),
                'securities_held_pre_transaction': self._clean_value(record.get('Number of Securities held Prior to acquisition/Disposed', '')),
                # Fixed: Use correct column name for securities type
                'securities_acquired_disposed_type': self._clean_value(record.get('Type of Securities Acquired/Disposed/Pledge etc.', '') or record.get('Type of Securities held Prior to acquisition/Disposed)', '')),
                'securities_acquired_disposed_number': self._clean_value(record.get('Number of Securities Acquired/Disposed/Pledge etc.', '')),
                'securities_acquired_disposed_value': self._clean_value(record.get('Value  of Securities Acquired/Disposed/Pledge etc', '')),
                'securities_acquired_disposed_transaction_type': self._clean_value(record.get('Transaction Type ( Buy/Sale/Pledge/Revoke/Invoke)', '')),
                'securities_held_post_transaction': self._clean_value(record.get('Number of Securities held Post  acquisition/Disposed/Pledge etc', '')),
                # Fixed: Improved period field construction with proper null handling
                'period': self._construct_period_field(record),
                'mode_of_acquisition': self._clean_value(record.get('Mode of Acquisition', '')),
                'trading_derivatives_type_contract': self._clean_value(record.get('Trading in Derivative - Type of Contract', '')),
                'trading_derivatives_buy_value': self._clean_value(record.get('Derivatives - Buy Value (Notional)', '')),
                'trading_derivatives_sale_value': self._clean_value(record.get('Derivatives - Sales Value (Notional)', '')),
                'reported_to_exchange': self._clean_value(record.get('Reported to Exchange', ''))
            }

            # Fetch NSE code using BSE security code for cross-referencing (with caching and enhanced retry mechanism)
            security_code = db_record.get('security_code', '')
            if security_code:
                # Check cache first to avoid repeated API calls
                if security_code in self.cross_reference_cache:
                    nse_code = self.cross_reference_cache[security_code]
                    if nse_code:  # Only set if not None
                        db_record['nse_code'] = nse_code
                else:
                    # Show which record is being processed for timeout debugging
                    if total_records > 1000:
                        print(f"🔍 Looking up cross-reference for BSE code: {security_code} (record {i}/{total_records})")

                    # Use more aggressive retry settings for large datasets to handle API load
                    nse_code = fetch_cross_reference_code('bse', security_code, self.supabase_url, self.supabase_key, max_retries=5, retry_delay=3)
                    # Cache the result (even if None) to avoid repeated lookups
                    self.cross_reference_cache[security_code] = nse_code
                    if nse_code:
                        db_record['nse_code'] = nse_code
                        if total_records > 1000:
                            print(f"✅ Found NSE code {nse_code} for BSE code {security_code}")

            # Generate hash using unified function
            db_record['record_hash'] = calculate_unified_hash(
                'bse',
                security_code=db_record.get('security_code', ''),
                name_of_person=db_record.get('name_of_person', ''),
                securities_acquired_disposed_number=db_record.get('securities_acquired_disposed_number', ''),
                securities_acquired_disposed_value=db_record.get('securities_acquired_disposed_value', ''),
                nse_code=db_record.get('nse_code', '')
            )

            # Validate critical fields - only skip records that are truly malformed
            security_code = db_record.get('security_code', '').strip() if db_record.get('security_code') else ''
            security_name = db_record.get('security_name', '').strip() if db_record.get('security_name') else ''
            name_of_person = db_record.get('name_of_person', '').strip() if db_record.get('name_of_person') else ''
            category_of_person = db_record.get('category_of_person', '').strip() if db_record.get('category_of_person') else ''
            record_hash = db_record.get('record_hash', '').strip() if db_record.get('record_hash') else ''

            # Only skip records where BOTH security_code AND security_name are missing
            # This allows records with null values in other fields to be processed
            if not security_code and not security_name:
                print(f"⚠️ Skipping record {i} - both security_code and security_name are empty (truly malformed)")
                continue

            # Also skip if record hash is missing (indicates processing error)
            if not record_hash:
                print(f"⚠️ Skipping record {i} - missing record hash")
                continue

            # Debug log for records with null values in important fields but valid security info
            if (not name_of_person or not category_of_person) and (security_code or security_name):
                print(f"ℹ️ Record {i} has null values but valid security info - keeping record: {security_code} - {security_name}")

            # Add the record (even if some fields are missing, as long as it's not completely empty)
            db_records.append(db_record)

        # Print cache statistics for large datasets
        if total_records > 100:
            cache_size = len(self.cross_reference_cache)
            print("📊 Cross-reference cache statistics:")
            print(f"   Cache size: {cache_size} unique BSE codes")
            print(f"   Total records processed: {total_records}")

        print(f"✅ Converted {len(db_records)} BSE records to database format (from {len(records)} input records)")
        return db_records
    
    def normalize_numeric_value(self, value):
        """
        Normalize numeric values by removing formatting characters and unnecessary decimal places.

        This function handles:
        - Removing commas from numeric strings (e.g., "3,000" -> "3000")
        - Removing trailing zeros after decimal (e.g., "75000.00" -> "75000")
        - Preserving meaningful decimals (e.g., "75000.50" -> "75000.50")
        - Handling various formats like "1,234,567.00" -> "1234567"

        Args:
            value: The value to normalize (can be any type)

        Returns:
            str or None: Normalized numeric value or None if it represents null
        """
        # First apply null normalization
        normalized_value = self.normalize_null_values(value)
        if normalized_value is None:
            return None

        # Convert to string for processing
        str_value = str(normalized_value).strip()

        # Check if this looks like a numeric value (contains digits, commas, or decimal points)
        if not re.match(r'^[\d,.-]+$', str_value):
            # Not a numeric pattern, return as-is
            return str_value

        try:
            # Remove commas from the string
            no_commas = str_value.replace(',', '')

            # Check if it contains a decimal point
            if '.' in no_commas:
                # Split into integer and decimal parts
                integer_part, decimal_part = no_commas.split('.', 1)

                # Remove trailing zeros from decimal part
                decimal_part = decimal_part.rstrip('0')

                # If decimal part is empty after removing zeros, return just the integer
                if not decimal_part:
                    return integer_part
                else:
                    return f"{integer_part}.{decimal_part}"
            else:
                # No decimal point, just return the number without commas
                return no_commas

        except Exception:
            # If any error occurs during numeric processing, return the original cleaned value
            return str_value

    def normalize_null_values(self, value):
        """
        Normalize field values by converting string representations of null values to actual Python None.

        This function handles various null representations commonly found in BSE data and converts
        them to proper Python None values, which will become SQL NULL in the database.

        Args:
            value: The value to normalize (can be any type)

        Returns:
            str or None: Cleaned value or None if it represents a null value
        """
        # Handle actual None values
        if value is None:
            return None

        # Handle non-string types that might represent null
        if isinstance(value, float):
            # Check for NaN values
            if str(value).lower() in ['nan', 'inf', '-inf']:
                return None
            # Convert valid float to string for consistency
            return str(value)

        # Convert to string and strip whitespace for processing
        if not isinstance(value, str):
            str_value = str(value)
        else:
            str_value = value

        # Strip whitespace
        str_value = str_value.strip()

        # Check for empty string or whitespace-only strings
        if not str_value:
            return None

        # Comprehensive list of null representations (case-insensitive)
        null_representations = {
            # Standard null representations
            'null', 'nil', 'none', 'na', 'n/a',
            # Common variations
            '#n/a', '#na', '#null', '#nil',
            # Database null representations
            'nan', 'inf', '-inf',
            # Empty/undefined representations
            'undefined', 'empty', 'void',
            # Single character representations
            '-', '_', '.',
            # Text representations
            'not available', 'not applicable', 'no data',
            'missing', 'unknown', 'tbd', 'to be determined'
        }

        # Check if the lowercase string value is in our null representations
        if str_value.lower() in null_representations:
            return None

        # Check for patterns that indicate null values
        # Single repeated characters (like "---", "...", "___") but NOT zeros
        if len(str_value) <= 5 and len(set(str_value)) == 1 and str_value[0] in '-_.':
            return None

        # Return the cleaned string value (original with stripped whitespace)
        return str_value

    def _clean_value(self, value):
        """
        Legacy method for backward compatibility.
        Now calls the enhanced normalize_numeric_value method for comprehensive cleaning.

        Args:
            value: The value to clean

        Returns:
            str or None: Cleaned value or None if it represents null
        """
        return self.normalize_numeric_value(value)

    def _construct_period_field(self, record):
        """
        Construct the period field from from_date and to_date with proper null handling.

        Args:
            record (dict): The record containing date fields

        Returns:
            str or None: Properly formatted period string or None if both dates are missing
        """
        from_date = record.get('Date of acquisition of shares/sale of shares/Date of Allotment(From date)', '')
        to_date = record.get('Date of acquisition of shares/sale of shares/Date of Allotment( To date  )', '')

        # Clean and normalize the date values
        from_date = self._clean_value(from_date)
        to_date = self._clean_value(to_date)

        # If both dates are None/empty, return None
        if not from_date and not to_date:
            return None

        # If only one date is available, use that one
        if from_date and not to_date:
            return from_date
        if not from_date and to_date:
            return to_date

        # If both dates are available, combine them
        return f"{from_date} to {to_date}"

    def _save_duplicates_to_csv(self, duplicate_records, duplicate_type="insert"):
        """
        Save duplicate records to CSV file for analysis

        Args:
            duplicate_records (list): List of duplicate records
            duplicate_type (str): Type of duplicate detection ('pre-check' or 'insert')
        """
        try:
            if not duplicate_records:
                return

            # Create duplicates folder if it doesn't exist
            duplicates_folder = os.path.join(os.path.dirname(__file__), '..', 'duplicates')
            os.makedirs(duplicates_folder, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"bse_insider_trading_duplicates_{duplicate_type}_{timestamp}.csv"
            filepath = os.path.join(duplicates_folder, filename)

            # Write duplicates to CSV
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                if duplicate_records:
                    # Get all unique fieldnames from all records to handle dynamic fields
                    all_fieldnames = set()
                    for record in duplicate_records:
                        all_fieldnames.update(record.keys())
                    fieldnames = sorted(list(all_fieldnames))

                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(duplicate_records)

            print(f"💾 Saved {len(duplicate_records)} BSE duplicate records to: {filename}")

        except Exception as e:
            print(f"⚠️ Error saving BSE duplicates to CSV: {e}")

    def _filter_duplicate_records(self, records):
        """Filter out records that already exist in the database"""
        if not records:
            return []
        
        try:
            # Extract all hashes from new records
            record_hashes = [record['record_hash'] for record in records]
            
            # Check existing hashes in batches
            existing_hashes = set()
            batch_size = 100
            
            for i in range(0, len(record_hashes), batch_size):
                batch = record_hashes[i:i + batch_size]
                batch_existing = self._check_existing_hashes(batch)
                existing_hashes.update(batch_existing)
            
            # Filter out duplicates and collect duplicate records
            new_records = []
            duplicate_records = []

            for record in records:
                if record['record_hash'] in existing_hashes:
                    duplicate_records.append(record)
                else:
                    new_records.append(record)

            # Save pre-check duplicates to CSV if any found
            if duplicate_records:
                self._save_duplicates_to_csv(duplicate_records, "pre_check")

            return new_records
            
        except Exception as e:
            print(f"⚠️ Error checking for duplicates: {e}")
            return records  # Return all records if duplicate check fails
    
    def _check_existing_hashes(self, hashes):
        """Check which hashes already exist in the database"""
        try:
            # Build query to check for existing hashes
            url = f"{self.supabase_url}/rest/v1/{self.table_name}"
            params = {
                'select': 'record_hash',
                'record_hash': f"in.({','.join(hashes)})"
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                existing_records = response.json()
                return {record['record_hash'] for record in existing_records}
            else:
                print(f"⚠️ Error checking existing hashes: {response.status_code}")
                return set()
                
        except Exception as e:
            print(f"⚠️ Error in hash check: {e}")
            return set()
    
    def _insert_records(self, records):
        """Insert new records into the database using batch processing"""
        print(f"💾 Inserting {len(records)} new BSE records to database...")

        total_inserted = 0
        total_errors = 0
        total_duplicates_at_insert = 0
        insert_duplicates = []  # Track records that are duplicates at insert time

        # Use batch processing for better performance
        batch_size = 100  # Supabase can handle up to 1000, but 100 is safer
        total_batches = (len(records) + batch_size - 1) // batch_size

        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(records))
            batch = records[start_idx:end_idx]

            try:
                url = f"{self.supabase_url}/rest/v1/{self.table_name}"

                response = requests.post(
                    url,
                    headers=self.headers,
                    data=json.dumps(batch),
                    timeout=60  # Longer timeout for batch operations
                )

                if response.status_code in [200, 201]:
                    batch_inserted = len(batch)
                    total_inserted += batch_inserted
                    print(f"✅ Batch {batch_num + 1}/{total_batches}: Inserted {batch_inserted} records ({total_inserted}/{len(records)} total)")
                elif response.status_code == 409:  # Some duplicates in batch
                    # Handle partial duplicates - need to process individually for this batch
                    batch_inserted, batch_errors, batch_duplicates, batch_duplicate_records = self._insert_batch_individually(batch, start_idx)
                    total_inserted += batch_inserted
                    total_errors += batch_errors
                    total_duplicates_at_insert += batch_duplicates
                    insert_duplicates.extend(batch_duplicate_records)
                    print(f"✅ Batch {batch_num + 1}/{total_batches}: {batch_inserted} inserted, {batch_duplicates} duplicates, {batch_errors} errors")
                else:
                    # Entire batch failed - try individual insertion
                    print(f"⚠️ Batch {batch_num + 1} failed ({response.status_code}), trying individual insertion...")
                    batch_inserted, batch_errors, batch_duplicates, batch_duplicate_records = self._insert_batch_individually(batch, start_idx)
                    total_inserted += batch_inserted
                    total_errors += batch_errors
                    total_duplicates_at_insert += batch_duplicates
                    insert_duplicates.extend(batch_duplicate_records)
                    print(f"✅ Batch {batch_num + 1}/{total_batches}: {batch_inserted} inserted, {batch_duplicates} duplicates, {batch_errors} errors")

                # Small delay between batches to avoid rate limiting
                if batch_num < total_batches - 1:  # Don't delay after the last batch
                    time.sleep(0.2)

            except Exception as e:
                print(f"❌ Error with batch {batch_num + 1}: {e}")
                # Try individual insertion for this batch
                batch_inserted, batch_errors, batch_duplicates, batch_duplicate_records = self._insert_batch_individually(batch, start_idx)
                total_inserted += batch_inserted
                total_errors += batch_errors
                total_duplicates_at_insert += batch_duplicates
                insert_duplicates.extend(batch_duplicate_records)

        # Final progress update
        if total_inserted > 0:
            print(f"✅ Completed: {total_inserted}/{len(records)} BSE records inserted")

        # Save insert-time duplicates to CSV
        if insert_duplicates:
            self._save_duplicates_to_csv(insert_duplicates, "insert")

        return total_inserted, total_errors, total_duplicates_at_insert

    def _insert_batch_individually(self, batch, start_idx):
        """Insert a batch of records individually when batch insertion fails"""
        batch_inserted = 0
        batch_errors = 0
        batch_duplicates = 0
        batch_duplicate_records = []

        for i, record in enumerate(batch):
            try:
                url = f"{self.supabase_url}/rest/v1/{self.table_name}"

                response = requests.post(
                    url,
                    headers=self.headers,
                    data=json.dumps([record]),
                    timeout=30
                )

                if response.status_code in [200, 201]:
                    batch_inserted += 1
                elif response.status_code == 409:  # Duplicate key constraint
                    batch_duplicates += 1
                    batch_duplicate_records.append(record)
                else:
                    batch_errors += 1
                    print(f"❌ Record {start_idx + i + 1} failed: {response.status_code}")

            except Exception as e:
                batch_errors += 1
                print(f"❌ Error inserting record {start_idx + i + 1}: {e}")

        return batch_inserted, batch_errors, batch_duplicates, batch_duplicate_records
