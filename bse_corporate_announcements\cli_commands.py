"""
Command Line Interface Commands for BSE Corporate Announcements Scraper
Handles execution of different CLI commands.
"""

import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .scraper import BSECorporateAnnouncementsScraper
    from .cli_parser import get_operation_mode, format_args_summary
    from .bse_config import bse_config as config
except ImportError:
    # Handle direct execution
    from scraper import BSECorporateAnnouncementsScraper
    from cli_parser import get_operation_mode, format_args_summary
    from bse_config import bse_config as config


def execute_command(args):
    """
    Execute the appropriate command based on parsed arguments.
    
    Args:
        args (argparse.Namespace): Parsed command line arguments
    """
    try:
        # Set up output verbosity
        setup_output_verbosity(args)
        
        # Get operation mode
        operation = get_operation_mode(args)
        
        # Show configuration summary unless in quiet mode
        if not args.quiet:
            print(format_args_summary(args))
            print()
        
        # Execute the appropriate command
        if operation == 'test':
            execute_test_connection(args)
        elif operation == 'info':
            execute_show_info(args)
        elif operation == 'cleanup':
            execute_cleanup(args)
        elif operation == 'scrape_days':
            execute_scrape_days(args)
        elif operation == 'scrape_range':
            execute_scrape_range(args)
        else:
            print(f"❌ Unknown operation mode: {operation}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def setup_output_verbosity(args):
    """
    Set up output verbosity based on arguments.
    
    Args:
        args (argparse.Namespace): Parsed arguments
    """
    # Apply custom output directory if specified
    if hasattr(args, 'output_dir') and args.output_dir:
        config.OUTPUT_FOLDER = args.output_dir
        print(f"📁 Using custom output directory: {args.output_dir}")
    
    # Apply custom timeout if specified
    if hasattr(args, 'timeout') and args.timeout:
        config.REQUEST_TIMEOUT = args.timeout
    
    # Note: Verbose and quiet modes are handled by individual functions


def execute_test_connection(args):
    """
    Execute connection test command.
    
    Args:
        args (argparse.Namespace): Parsed arguments
    """
    print("🔌 Testing BSE Corporate Announcements API connection...")
    print()
    
    try:
        with BSECorporateAnnouncementsScraper() as scraper:
            success = scraper.test_connection()
            
            if success:
                print("✅ Connection test passed!")
                print("🎉 BSE Corporate Announcements API is accessible")
                sys.exit(0)
            else:
                print("❌ Connection test failed!")
                print("💡 Please check your internet connection and try again")
                sys.exit(1)
                
    except Exception as e:
        print(f"❌ Connection test error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def execute_show_info(args):
    """
    Execute show information command.
    
    Args:
        args (argparse.Namespace): Parsed arguments
    """
    print("ℹ️ BSE Corporate Announcements Scraper Information")
    print("=" * 50)
    
    try:
        with BSECorporateAnnouncementsScraper() as scraper:
            info = scraper.get_scraper_info()
            
            # Display basic information
            print(f"Scraper Name: {info['scraper_name']}")
            print(f"Version: {info['version']}")
            print(f"API Base URL: {info['api_base_url']}")
            print(f"API Endpoint: {info['api_endpoint']}")
            print(f"Output Folder: {info['output_folder']}")
            print(f"Default Days Back: {info['default_days_back']}")
            print()
            
            # Display feature status
            print("Features:")
            print(f"  CSV Export: {'✅ Enabled' if info['csv_enabled'] else '❌ Disabled'}")
            print(f"  Database Storage: {'✅ Enabled' if info['database_enabled'] else '❌ Disabled'}")
            print()
            
            # Display database information if available
            if 'database_info' in info:
                db_info = info['database_info']
                print("Database Configuration:")
                print(f"  Table: {db_info.get('table_name', 'N/A')}")
                print(f"  Status: {db_info.get('status', 'Unknown')}")
                if 'error' in db_info:
                    print(f"  Error: {db_info['error']}")
                print()
            
            # Test connection
            print("Connection Status:")
            connection_ok = scraper.test_connection()
            print(f"  API Connection: {'✅ OK' if connection_ok else '❌ Failed'}")
            
            sys.exit(0)
            
    except Exception as e:
        print(f"❌ Error getting scraper information: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def execute_cleanup(args):
    """
    Execute cleanup command.
    
    Args:
        args (argparse.Namespace): Parsed arguments
    """
    print(f"🧹 Cleaning up old files (keeping {args.cleanup} most recent)...")
    print()
    
    try:
        with BSECorporateAnnouncementsScraper() as scraper:
            scraper.cleanup_old_files(args.cleanup)
            print("✅ Cleanup completed successfully!")
            sys.exit(0)
            
    except Exception as e:
        print(f"❌ Cleanup error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def execute_scrape_days(args):
    """
    Execute scrape by days command.
    
    Args:
        args (argparse.Namespace): Parsed arguments
    """
    print(f"📅 Scraping BSE Corporate Announcements for the last {args.days} days...")
    print()
    
    try:
        # Override configuration based on arguments
        if args.no_csv:
            config.SAVE_CSV = False
        if args.no_database:
            config.USE_DATABASE = False
        
        with BSECorporateAnnouncementsScraper() as scraper:
            result = scraper.scrape_recent_data(days_back=args.days)
            
            if result:
                display_scrape_results(result, args)
                sys.exit(0)
            else:
                print("❌ Scraping failed!")
                print("💡 Try running with --test-connection to check API access")
                sys.exit(1)
                
    except Exception as e:
        print(f"❌ Scraping error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def execute_scrape_range(args):
    """
    Execute scrape by date range command.
    
    Args:
        args (argparse.Namespace): Parsed arguments
    """
    print(f"📅 Scraping BSE Corporate Announcements from {args.start_date} to {args.end_date}...")
    print()
    
    try:
        # Override configuration based on arguments
        if args.no_csv:
            config.SAVE_CSV = False
        if args.no_database:
            config.USE_DATABASE = False
        
        with BSECorporateAnnouncementsScraper() as scraper:
            result = scraper.scrape_date_range(args.start_date, args.end_date)
            
            if result:
                display_scrape_results(result, args)
                sys.exit(0)
            else:
                print("❌ Scraping failed!")
                print("💡 Try running with --test-connection to check API access")
                sys.exit(1)
                
    except Exception as e:
        print(f"❌ Scraping error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def display_scrape_results(result, args):
    """
    Display the results of a scraping operation.
    
    Args:
        result (dict): Scraping result dictionary
        args (argparse.Namespace): Parsed arguments
    """
    print("🎉 Scraping completed successfully!")
    print()
    
    # Basic results
    print("Results Summary:")
    print(f"  📊 Records found: {result['record_count']}")
    print(f"  📅 Date range: {result['start_date'].strftime('%d/%m/%Y')} to {result['end_date'].strftime('%d/%m/%Y')}")
    
    # File outputs
    if result.get('files_saved'):
        print(f"  💾 Files saved: {len(result['files_saved'])}")
        if args.verbose:
            for file_path in result['files_saved']:
                print(f"    - {file_path}")
    
    # Database results
    if result.get('database_result'):
        db_result = result['database_result']
        if db_result.get('success'):
            print(f"  🗄️ Database: {db_result.get('new_records', 0)} new records saved")
            if db_result.get('duplicate_records', 0) > 0:
                print(f"    (Skipped {db_result['duplicate_records']} duplicates)")
        else:
            print(f"  🗄️ Database: ❌ Failed ({db_result.get('error', 'Unknown error')})")
    
    # Data summary
    if args.verbose and result.get('summary'):
        summary = result['summary']
        print()
        print("Data Details:")
        print(f"  Columns: {summary.get('column_count', 0)}")
        if 'date_range' in summary and isinstance(summary['date_range'], dict):
            for col, date_info in summary['date_range'].items():
                if isinstance(date_info, dict) and 'min_date' in date_info:
                    print(f"  {col}: {date_info['min_date']} to {date_info['max_date']}")
    
    print()
    print("✅ Operation completed successfully!")
