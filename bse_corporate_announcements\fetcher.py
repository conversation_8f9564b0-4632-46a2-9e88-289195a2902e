"""
Data Fetcher for BSE Corporate Announcements Scraper
Handles API requests to fetch corporate announcements data from BSE.
"""

import time
import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .bse_config import bse_config as config
except ImportError:
    # Handle direct execution
    from bse_config import bse_config as config


class DataFetcher:
    """
    Handles fetching corporate announcements data from BSE API.
    Uses the session manager for HTTP requests with proper headers and proxy support.
    """

    def __init__(self, session_manager):
        """
        Initialize the data fetcher.
        
        Args:
            session_manager: SessionManager instance for making HTTP requests
        """
        self.session = session_manager
        self.api_base_url = config.API_BASE_URL
        self.api_endpoint = config.CORPORATE_ANNOUNCEMENTS_API_URL

    def fetch_data(self, start_date, end_date, page_no=1):
        """
        Main method to fetch corporate announcements data for a date range.
        
        Args:
            start_date (datetime): Start date for data fetching
            end_date (datetime): End date for data fetching
            page_no (int): Page number for pagination (default: 1)
            
        Returns:
            dict or None: API response data or None if failed
        """
        try:
            # Format dates for BSE API
            start_date_str = self.format_date_for_bse(start_date)
            end_date_str = self.format_date_for_bse(end_date)

            print(f"Fetching BSE Corporate Announcements from {start_date_str} to {end_date_str} (Page {page_no})")

            # Build API URL and parameters
            api_url = f"{self.api_base_url}{self.api_endpoint}"
            params = self.build_api_params(start_date_str, end_date_str, page_no)

            # Make the API request
            response = self.session.make_api_request(api_url, params=params, timeout=config.REQUEST_TIMEOUT)

            if response and response.status_code == 200:
                try:
                    # Check content type
                    content_type = response.headers.get('Content-Type', '').lower()
                    print(f"Response Content-Type: {content_type}")

                    # If ScraperAPI returns HTML, try direct request
                    if 'text/html' in content_type or response.text.strip().startswith('<'):
                        print("⚠️ Received HTML response, trying direct API request...")
                        import requests
                        direct_response = requests.get(api_url, params=params, headers=self.session.get_exchange_specific_headers(), timeout=config.REQUEST_TIMEOUT)

                        if direct_response.status_code == 200:
                            response = direct_response
                            print("✅ Direct API request successful")
                        else:
                            print(f"❌ Direct API request also failed: {direct_response.status_code}")

                    json_data = response.json()
                    print(f"✅ Successfully fetched data: {len(json_data.get('Table', []))} records")
                    return {
                        'json_data': json_data,
                        'start_date': start_date,
                        'end_date': end_date,
                        'page_no': page_no,
                        'url': api_url,
                        'params': params,
                        'response_text': response.text
                    }
                except ValueError as e:
                    print(f"❌ Failed to parse JSON response: {e}")
                    print(f"Response content (first 200 chars): {response.text[:200]}")
                    return None
            else:
                print(f"❌ API request failed with status: {response.status_code if response else 'None'}")
                return None

        except Exception as e:
            print(f"Error in fetch_data: {e}")
            return None

    def fetch_all_pages(self, start_date, end_date, max_pages=10):
        """
        Fetch all pages of data for a date range.

        Note: BSE API seems to work best with single-day queries.
        For date ranges, we iterate through each day individually.

        Args:
            start_date (datetime): Start date for data fetching
            end_date (datetime): End date for data fetching
            max_pages (int): Maximum number of pages to fetch per day (safety limit)

        Returns:
            list: List of all fetched data dictionaries
        """
        all_data = []

        # Check if this is a single day query
        if start_date.date() == end_date.date():
            print(f"Fetching data for single day: {start_date.strftime('%Y%m%d')}")
            return self.fetch_all_pages_for_single_day(start_date, max_pages)

        # For date ranges, iterate through each day
        print(f"Fetching data for date range: {start_date.strftime('%Y%m%d')} to {end_date.strftime('%Y%m%d')}")
        current_date = start_date
        total_days = (end_date - start_date).days + 1

        while current_date <= end_date:
            day_str = current_date.strftime('%Y%m%d')
            print(f"Fetching data for day {current_date.strftime('%d/%m/%Y')} ({(current_date - start_date).days + 1}/{total_days})")

            # Fetch data for this specific day
            day_data = self.fetch_all_pages_for_single_day(current_date, max_pages)

            if day_data:
                all_data.extend(day_data)
                print(f"  ✅ Found {sum(len(d.get('json_data', {}).get('Table', [])) for d in day_data)} records for {current_date.strftime('%d/%m/%Y')}")
            else:
                print(f"  ℹ️ No data found for {current_date.strftime('%d/%m/%Y')}")

            # Move to next day
            current_date += timedelta(days=1)

            # Add delay between days to be respectful
            if current_date <= end_date:
                time.sleep(0.5)

        print(f"Total pages fetched across all days: {len(all_data)}")
        return all_data

    def fetch_all_pages_for_single_day(self, date, max_pages=10):
        """
        Fetch all pages of data for a single day.

        Args:
            date (datetime): Date for data fetching
            max_pages (int): Maximum number of pages to fetch (safety limit)

        Returns:
            list: List of all fetched data dictionaries for this day
        """
        all_data = []
        page_no = 1

        while page_no <= max_pages:
            result = self.fetch_data(date, date, page_no)

            if result and result.get('json_data'):
                json_data = result['json_data']
                records = json_data.get('Table', [])

                if records:
                    all_data.append(result)
                    if page_no == 1:  # Only print for first page to reduce noise
                        print(f"    Page {page_no}: Found {len(records)} records")

                    # Check if there are more pages
                    # If the current page has fewer records than expected, we might be at the end
                    if len(records) < config.DEFAULT_PAGE_SIZE:
                        if page_no > 1:
                            print(f"    Reached end of data at page {page_no}")
                        break

                    page_no += 1

                    # Add delay between requests to be respectful
                    time.sleep(1)
                else:
                    if page_no == 1:
                        # No records on first page means no data for this day
                        break
                    else:
                        print(f"    No records found on page {page_no}, stopping pagination")
                        break
            else:
                if page_no == 1:
                    # Failed to fetch first page means no data for this day
                    break
                else:
                    print(f"    Failed to fetch page {page_no}, stopping pagination")
                    break

        return all_data

    def build_api_params(self, start_date_str, end_date_str, page_no=1):
        """
        Build API parameters for the BSE corporate announcements request.
        Based on the curl command provided by the user.
        
        Args:
            start_date_str (str): Start date in BSE format (YYYYMMDD)
            end_date_str (str): End date in BSE format (YYYYMMDD)
            page_no (int): Page number for pagination
            
        Returns:
            dict: API parameters
        """
        return {
            "pageno": page_no,
            "strCat": config.DEFAULT_CATEGORY,
            "strPrevDate": start_date_str,
            "strScrip": config.DEFAULT_SCRIP,
            "strSearch": config.DEFAULT_SEARCH,
            "strToDate": end_date_str,
            "strType": config.DEFAULT_TYPE,
            "subcategory": config.DEFAULT_SUBCATEGORY
        }

    def format_date_for_bse(self, date_obj):
        """
        Format datetime object to BSE API date format (YYYYMMDD).
        
        Args:
            date_obj (datetime): Date to format
            
        Returns:
            str: Formatted date string
        """
        return date_obj.strftime(config.BSE_DATE_FORMAT)

    def test_api_connection(self):
        """
        Test the API connection with a simple request.
        
        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            print("Testing BSE Corporate Announcements API connection...")
            
            # Use today's date for testing
            test_date = datetime.now()
            result = self.fetch_data(test_date, test_date, page_no=1)
            
            if result:
                print("✅ API connection test successful")
                return True
            else:
                print("❌ API connection test failed")
                return False
                
        except Exception as e:
            print(f"Error testing API connection: {e}")
            return False
